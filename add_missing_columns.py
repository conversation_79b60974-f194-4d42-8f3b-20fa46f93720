"""
<PERSON><PERSON><PERSON> to add missing columns to the companyscore table for free trial functionality.
This script adds columns that are referenced in the app.py but missing from the database schema.
"""

import mysql.connector

# Database configuration
DB_CONFIG = {
    'host': 'fz-website-registration.c9uy0agkwmgg.ap-south-1.rds.amazonaws.com',
    'user': 'admin',
    'password': 'master123',
    'database': 'registration'
}

def add_missing_columns():
    """Add missing columns to the companyscore table."""
    
    # List of missing columns that need to be added
    missing_columns = [
        # Culture engagement columns (these seem to be missing from schema)
        "culture_engagement_score DECIMAL(5,2)",
        "culture_engagement_positive_pct DECIMAL(5,2)", 
        "culture_engagement_neutral_pct DECIMAL(5,2)",
        "culture_engagement_negative_pct DECIMAL(5,2)",
    ]
    
    try:
        # Connect to database
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # Add each missing column
        for column_def in missing_columns:
            try:
                alter_query = f"ALTER TABLE companyscore ADD COLUMN {column_def}"
                cursor.execute(alter_query)
                print(f"✅ Added column: {column_def}")
            except mysql.connector.Error as err:
                if err.errno == 1060:  # Duplicate column name
                    print(f"⚠️  Column already exists: {column_def.split()[0]}")
                else:
                    print(f"❌ Error adding column {column_def}: {err}")
        
        # Commit changes
        conn.commit()
        print("✅ All missing columns processed successfully!")
        
    except mysql.connector.Error as err:
        print(f"❌ Database connection error: {err}")
        
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    add_missing_columns()
